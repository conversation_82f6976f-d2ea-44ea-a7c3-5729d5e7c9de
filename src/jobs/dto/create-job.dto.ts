// src/jobs/dto/create-job.dto.ts
import {
  IsString,
  IsNumber,
  IsNotEmpty,
  IsOptional,
  IsIn,
  IsArray,
} from 'class-validator';

export class CreateJobDto {
  @IsString()
  @IsNotEmpty()
  job_name: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['SEW', 'YVW'])
  job_type: 'SEW' | 'YVW';

  @IsOptional()
  @IsNumber()
  template_id?: number | null;

  @IsNumber()
  @IsNotEmpty()
  user_id: number;

  @IsNumber()
  @IsNotEmpty()
  client_id: number;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  document_id?: number[];

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsNotEmpty()
  status: string;
}
