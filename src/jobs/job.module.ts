// src/jobs/job.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Job } from './job.entity';
import { JobService } from './job.service';
import { JobController } from './job.controller';
import { User } from '../users/user.entity';
import { Template } from '../templates/template.entity';
import { Document } from '../documents/document.entity';
import { Client } from '../clients/client.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Job, User, Template, Document, Client])],
  controllers: [JobController],
  providers: [JobService],
})
export class JobModule {}
