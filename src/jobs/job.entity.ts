// src/jobs/job.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from '../users/user.entity';
import { Template } from '../templates/template.entity';
import { Client } from '../clients/client.entity';
import { ExtractedData } from 'src/extracteData/extracted-data.entity';

@Entity('jobs')
export class Job {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  job_name: string;

  @Column({ type: 'varchar', length: 10 })
  job_type: 'SEW' | 'YVW';

  @Column()
  status: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ name: 'template_id', nullable: true })
  templateId: number | null;

  @Column({ name: 'document_id', type: 'int', array: true, nullable: true })
  documentId: number[];

  @Column({ nullable: true, name: 'user_id' })
  userId: number;
  @Column({ nullable: true, name: 'client_id' })
  clientId: number;

  @ManyToOne(() => Template, (template) => template.jobs, { nullable: true })
  @JoinColumn({ name: 'template_id' })
  template: Template | null;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Client)
  @JoinColumn({ name: 'client_id' })
  client: Client;

  @OneToMany(() => ExtractedData, (data) => data.job)
  extractedData: ExtractedData[];
}
