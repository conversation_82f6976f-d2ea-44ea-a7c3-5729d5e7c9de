// src/jobs/job.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from './job.entity';
import { CreateJobDto } from './dto/create-job.dto';
import { User } from '../users/user.entity';
import { Template } from '../templates/template.entity';
import { Document } from '../documents/document.entity';
import { Client } from '../clients/client.entity';

@Injectable()
export class JobService {
  constructor(
    @InjectRepository(Job) private jobRepo: Repository<Job>,
    @InjectRepository(User) private userRepo: Repository<User>,
    @InjectRepository(Template) private templateRepo: Repository<Template>,
    @InjectRepository(Document) private documentRepo: Repository<Document>,
    @InjectRepository(Client) private clientRepo: Repository<Client>,
  ) {}

  async findAll(
    page?: number,
    limit?: number,
    searchTerm?: string,
    clientId?: number,
    jobTypeId?: string,
    userId?: string,
  ): Promise<{ jobs: Job[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit || undefined;

    const query = this.jobRepo
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.user', 'user')
      .leftJoinAndSelect('job.template', 'template')

      .leftJoinAndSelect('job.client', 'client')
      .orderBy('job.id', 'DESC')
      .skip(skip)
      .take(take);

    if (searchTerm) {
      query.andWhere(
        `job.job_name ILIKE :search
        OR job.description ILIKE :search
        OR job.status ILIKE :search
        OR user.name ILIKE :search
        OR user.email ILIKE :search
        OR template.name ILIKE :search
        OR document.file_name ILIKE :search
        OR client.name ILIKE :search
        OR client.email ILIKE :search`,
        { search: `%${searchTerm}%` },
      );
    }

    if (clientId) {
      query.andWhere('job.clientId = :clientId', { clientId });
    }

    if (jobTypeId) {
      query.andWhere('LOWER(job.job_type) = LOWER(:jobTypeId)', { jobTypeId });
    }

    if (userId) {
      query.andWhere('user.id = :userId', { userId });
    }

    const [jobs, totalCount] = await query.getManyAndCount();

    for (const job of jobs) {
      if (job.documentId?.length) {
        job['documents'] = await this.documentRepo.findByIds(job.documentId);
      } else {
        job['documents'] = [];
      }
    }

    return { jobs, totalCount };
  }

  async findOne(id: number): Promise<Job | null> {
    const job = await this.jobRepo.findOne({
      where: { id },
      relations: ['user', 'template', 'client'], // Removed document relation
    });

    if (!job) return null;

    // Manually fetch documents for this job
    if (job.documentId?.length) {
      job['documents'] = await this.documentRepo.findByIds(job.documentId);
    } else {
      job['documents'] = [];
    }

    return job;
  }

  async create(dto: CreateJobDto): Promise<Job> {
    const user = await this.userRepo.findOneBy({ id: dto.user_id });
    if (!user) throw new NotFoundException('User not found');

    let template: Template | null = null;
    if (dto.template_id && dto.template_id > 0) {
      template = await this.templateRepo.findOneBy({ id: dto.template_id });
      if (!template) throw new NotFoundException('Template not found');
    }

    const client = await this.clientRepo.findOneBy({ id: dto.client_id });
    if (!client) throw new NotFoundException('Client not found');

    const documentId = dto.document_id;
    const job = this.jobRepo.create({
      job_name: dto.job_name,
      job_type: dto.job_type,
      status: dto.status,
      description: dto.description,
      user,
      template: template || undefined,
      client,
      documentId,
    });

    return this.jobRepo.save(job);
  }

  async update(id: number, dto: Partial<CreateJobDto>): Promise<Job> {
    const job = await this.findOne(id);
    if (!job) throw new NotFoundException('Job not found');

    if (dto.job_name !== undefined) job.job_name = dto.job_name;
    if (dto.job_type !== undefined) job.job_type = dto.job_type;
    if (dto.status !== undefined) job.status = dto.status;
    if (dto.description !== undefined) job.description = dto.description;

    if (dto.user_id !== undefined && dto.user_id !== null) {
      const user = await this.userRepo.findOneBy({ id: dto.user_id });
      if (!user) throw new NotFoundException('User not found');
      job.user = user;
      job.userId = user.id;
    }

    if (dto.client_id !== undefined && dto.client_id !== null) {
      const client = await this.clientRepo.findOneBy({ id: dto.client_id });
      if (!client) throw new NotFoundException('Client not found');
      job.client = client;
      job.clientId = client.id;
    }

    if (dto.template_id !== undefined) {
      if (dto.template_id && dto.template_id > 0) {
        const template = await this.templateRepo.findOneBy({
          id: dto.template_id,
        });
        if (!template) throw new NotFoundException('Template not found');
        job.template = template;
        job.templateId = template.id;
      } else {
        job.template = null;
        job.templateId = null;
      }
    }

    if (dto.document_id !== undefined) {
      job.documentId = dto.document_id;
    }

    return this.jobRepo.save(job);
  }

  async delete(id: number): Promise<{ affected?: number | null }> {
    const result = await this.jobRepo.delete(id);
    if (result.affected === 0) throw new NotFoundException('Job not found');
    return result;
  }
}
