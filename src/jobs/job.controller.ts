// src/jobs/job.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { JobService } from './job.service';
import { CreateJobDto } from './dto/create-job.dto';
import { createResponse } from '../common/utils/responseHandler';
import { MESSAGES } from '../common/utils/messageConfig';

@Controller('jobs')
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @Get()
  async getAllJobs(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('clientId') clientId?: string,
    @Query('jobTypeId') jobTypeId?: string,
    @Query('userId') userId?: string,
  ) {
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;
    const processedClientId =
      clientId && clientId !== 'undefined' ? Number(clientId) : undefined;

    const processedJobTypeId =
      jobTypeId && jobTypeId !== 'undefined' ? jobTypeId : undefined;

    const processedUserId =
      userId && userId !== 'undefined' ? userId : undefined;

    const { jobs, totalCount } = await this.jobService.findAll(
      pageNumber,
      limitNumber,
      processedSearchTerm,
      processedClientId,
      processedJobTypeId,
      processedUserId,
    );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(200, MESSAGES.JOB.FETCHED, jobs, {
      totalCount,
      page: pageNumber || 'All',
      limit: limitNumber || 'All',
      totalPages,
    });
  }

  @Get(':id')
  async getJobById(@Param('id') id: string) {
    const job = await this.jobService.findOne(+id);
    if (!job) throw new NotFoundException(MESSAGES.JOB.NOT_FOUND);
    return createResponse(200, MESSAGES.JOB.FETCHED, job);
  }

  @Post()
  async createJob(@Body() dto: CreateJobDto) {
    const job = await this.jobService.create(dto);
    return createResponse(201, MESSAGES.JOB.CREATED, job);
  }

  @Put(':id')
  async updateJob(@Param('id') id: string, @Body() dto: Partial<CreateJobDto>) {
    const job = await this.jobService.update(+id, dto);
    return createResponse(200, MESSAGES.JOB.UPDATED, job);
  }

  @Delete(':id')
  async deleteJob(@Param('id') id: string) {
    await this.jobService.delete(+id);
    return createResponse(200, MESSAGES.JOB.DELETED);
  }
}
