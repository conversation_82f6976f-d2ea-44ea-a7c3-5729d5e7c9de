import {
  IsN<PERSON>ber,
  Is<PERSON>rray,
  <PERSON>idateNested,
  <PERSON><PERSON>ptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';

class ExtractFieldDto {
  @IsString()
  value: string;
}

class SectionDto {
  @IsString()
  sectionName: string;

  @IsOptional()
  @IsString()
  contains?: string;

  @IsOptional()
  @IsString()
  match_value?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExtractFieldDto)
  extract_fields: ExtractFieldDto[];
}

export class CreateTemplateDto {
  @IsString()
  name: string;

  @IsNumber()
  created_by: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionDto)
  sections: SectionDto[];
}
