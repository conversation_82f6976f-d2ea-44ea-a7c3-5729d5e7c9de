import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplateService } from './template.service';
import { Template } from './template.entity';
import { User } from '../users/user.entity';
import { TemplateController } from './template.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Template, User])],
  controllers: [TemplateController],
  providers: [TemplateService],
  exports: [TemplateService],
})
export class TemplateModule {}
