import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Template } from './template.entity';
import { CreateTemplateDto } from './dto/create-template.dto';
import { User } from '../users/user.entity';

@Injectable()
export class TemplateService {
  constructor(
    @InjectRepository(Template)
    private templateRepo: Repository<Template>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  async createTemplate(dto: CreateTemplateDto): Promise<Template> {
    const user = await this.userRepo.findOneBy({ id: Number(dto.created_by) });
    if (!user) throw new NotFoundException('User not found');

    const config = dto.sections.map((section) => {
      const extractFields = section.extract_fields
        .map((field) => field.value.trim())
        .filter(Boolean);

      const sectionData: {
        sectionName: string;
        extract_fields: string[];
        contains?: string[];
        match_value?: string;
      } = {
        sectionName: section.sectionName.trim(),
        extract_fields: extractFields,
      };

      if (section.contains?.trim()) {
        sectionData.contains = [section.contains.trim()];
      }

      if (section.match_value?.trim()) {
        sectionData.match_value = section.match_value.trim();
      }

      return sectionData;
    });

    const template = this.templateRepo.create({
      name: dto.name,
      config_json: JSON.stringify(config),
      created_by: user,
    });

    return this.templateRepo.save(template);
  }

  async getAllTemplates(
    page?: number,
    limit?: number,
    searchTerm?: string,
  ): Promise<{ templates: Template[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit;

    const query = this.templateRepo
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.created_by', 'user')
      .orderBy('template.id', 'DESC')
      .skip(skip)
      .take(take);

    if (searchTerm) {
      query.andWhere(
        `template.name ILIKE :search
        OR template.description ILIKE :search
        OR user.name ILIKE :search
        OR user.email ILIKE :search`,
        { search: `%${searchTerm}%` },
      );
    }

    const [templates, totalCount] = await query.getManyAndCount();

    return { templates, totalCount };
  }

  async getTemplateById(id: number): Promise<Template> {
    const template = await this.templateRepo.findOne({
      where: { id },
      relations: ['created_by'],
    });
    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return template;
  }

  async deleteTemplate(id: number): Promise<void> {
    const result = await this.templateRepo.delete(id);
    if (!result.affected) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
  }
}
