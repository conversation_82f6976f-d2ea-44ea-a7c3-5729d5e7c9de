import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { TemplateService } from './template.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { createResponse } from '../common/utils/responseHandler';
import { MESSAGES } from '../common/utils/messageConfig';

@Controller('templates')
export class TemplateController {
  constructor(private readonly templateService: TemplateService) {}

  @Post()
  async create(@Body() dto: CreateTemplateDto) {
    const created = await this.templateService.createTemplate(dto);
    return createResponse(201, MESSAGES.TEMPLATE.CREATED, created);
  }

  @Get()
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;

    const { templates, totalCount } =
      await this.templateService.getAllTemplates(
        pageNumber,
        limitNumber,
        processedSearchTerm,
      );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(200, MESSAGES.TEMPLATE.FETCHED, templates, {
      totalCount,
      page: pageNumber || 'All',
      limit: limitNumber || 'All',
      totalPages,
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const found = await this.templateService.getTemplateById(+id);
    if (!found) throw new NotFoundException(MESSAGES.TEMPLATE.NOT_FOUND);
    return createResponse(200, MESSAGES.TEMPLATE.FETCHED, found);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.templateService.deleteTemplate(+id);
    return createResponse(200, MESSAGES.TEMPLATE.DELETED);
  }
}
