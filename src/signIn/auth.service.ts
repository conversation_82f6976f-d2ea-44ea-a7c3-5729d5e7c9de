import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { CreateAuthDto } from './dto/auth.dto';
import { User } from 'src/users/user.entity';
import { comparePasswords } from 'src/common/utils/bcrypt';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    private readonly jwtService: JwtService,
  ) {}

  async createLogin(
    createAuthDto: CreateAuthDto,
  ): Promise<{ user: Partial<User> & { api_token: string } }> {
    const { email, password } = createAuthDto;

    const user = await this.usersRepository.findOne({ where: { email } });

    if (!user) {
      throw new UnauthorizedException('Invalid email');
    }

    if (!user.password || !password) {
      throw new UnauthorizedException('Password is required');
    }

    const isPasswordValid = await comparePasswords(password, user.password);

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid password');
    }

    // Generate JWT token
    const payload = { sub: user.id, email: user.email };
    const token = this.jwtService.sign(payload, {
      expiresIn: process.env.JWT_EXPIRES_IN || '1h', // Default token expiry to 1 hour
    });

    // Save the token
    await this.usersRepository.update(user.id, { api_token: token });

    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        is_active: user.is_active,
        api_token: token,
      },
    };
  }
}
