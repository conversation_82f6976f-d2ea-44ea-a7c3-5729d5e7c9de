import {
  Body,
  Controller,
  HttpStatus,
  Post,
  UseFilters,
  UsePipes,
} from '@nestjs/common';
import { HttpExceptionFilter } from 'src/common/filters/http-exception.filter';
import { ValidationPipe } from 'src/common/pipes/validation.pipe';
import { CreateAuthDto } from './dto/auth.dto';
import { createResponse } from 'src/common/utils/responseHandler';
import { AuthService } from './auth.service';
import { MESSAGES } from 'src/common/utils/messageConfig';

@Controller('login')
@UseFilters(HttpExceptionFilter)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  @UsePipes(new ValidationPipe())
  async createLoginController(
    @Body() createAuthDto: CreateAuthDto,
  ): Promise<any> {
    const user = await this.authService.createLogin(createAuthDto);

    return user
      ? createResponse(HttpStatus.CREATED, MESSAGES.AUTH.SUCCESS, user)
      : createResponse(HttpStatus.BAD_REQUEST, MESSAGES.AUTH.FAILED, null);
  }
}
