import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import { Document } from '../documents/document.entity';
import { Template } from 'src/templates/template.entity';
import { User } from 'src/users/user.entity';
import { Job } from 'src/jobs/job.entity';
import { Client } from 'src/clients/client.entity';
import { ExtractedData } from 'src/extracteData/extracted-data.entity';
import { ExtractedDataFile } from 'src/extracteDataFiles/extracteDataFile.entity';
dotenv.config();

export const typeOrmConfig: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: [
    Document,
    Template,
    User,
    Job,
    Client,
    ExtractedData,
    ExtractedDataFile,
  ],
  synchronize: false,
  logging: ['query', 'error'],
  ssl: false,
};

const dataSource = new DataSource(typeOrmConfig);

dataSource
  .initialize()
  .then(() => {
    console.log('✅ Database connection established successfully!');
  })
  .catch((error) => {
    console.error('❌ Database connection failed:', error);
  });

export default dataSource;
