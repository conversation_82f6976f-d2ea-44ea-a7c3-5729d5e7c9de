import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import { Document } from '../documents/document.entity';
import { Template } from 'src/templates/template.entity';
import { User } from 'src/users/user.entity';
import { Job } from 'src/jobs/job.entity';
import { Client } from 'src/clients/client.entity';
import { ExtractedData } from 'src/extracteData/extracted-data.entity';
import { ExtractedDataFile } from 'src/extracteDataFiles/extracteDataFile.entity';

// Load environment variables
dotenv.config();

// Debug environment variables
console.log('🔍 Database Configuration Debug:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***HIDDEN***' : 'NOT SET');

export const typeOrmConfig: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sundocx_db',
  entities: [
    Document,
    Template,
    User,
    Job,
    Client,
    ExtractedData,
    ExtractedDataFile,
  ],
  synchronize: false,
  logging: ['query', 'error'],
  ssl: false,
};

// Create DataSource instance but don't initialize immediately
// Let NestJS handle the connection initialization
const dataSource = new DataSource(typeOrmConfig);

export default dataSource;
