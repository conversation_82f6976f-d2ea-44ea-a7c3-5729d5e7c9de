import { User } from 'src/users/user.entity';
import { Client } from 'src/clients/client.entity'; // ← make sure this exists
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('documents')
export class Document {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  file_name: string;

  @Column('text')
  file_path: string;

  @Column({ nullable: true })
  file_type: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  process_type: string; // ✅ NEW

  @Column({ nullable: true, name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ nullable: true, name: 'client_id' })
  clientId: number; // ✅ NEW

  @ManyToOne(() => Client, { nullable: true })
  @JoinColumn({ name: 'client_id' })
  client: Client; // ✅ NEW

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
