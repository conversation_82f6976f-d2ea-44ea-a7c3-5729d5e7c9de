/* eslint-disable @typescript-eslint/no-require-imports */
import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  BadRequestException,
  Delete,
  Param,
  Put,
  Get,
  NotFoundException,
  Query,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { S3Client } from '@aws-sdk/client-s3';
import multerS3 = require('multer-s3');
import { Document } from './document.entity';
import { AUTO_CONTENT_TYPE as AUTO_CONTENT_TYPE_RAW } from 'multer-s3';
import type { StorageEngine } from 'multer';
import type { Request } from 'express';
import { DocumentService } from './document.service';
import { createResponse } from '../common/utils/responseHandler';
import { MESSAGES } from '../common/utils/messageConfig';
import { UpdateDocumentDto } from './dto/upload-document.dto';
import { extname } from 'path';

export interface S3File extends Express.Multer.File {
  location: string;
  key: string;
  bucket: string;
}

const AUTO_CONTENT_TYPE = AUTO_CONTENT_TYPE_RAW as string | false | undefined;

@Controller('documents')
export class DocumentController {
  private readonly s3: S3Client;

  constructor(private readonly documentService: DocumentService) {
    if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      throw new Error('AWS credentials are not configured');
    }

    this.s3 = new S3Client({
      region: process.env.AWS_REGION || 'ap-south-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  @Get()
  async getAllDocuments(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;

    const { documents, totalCount } = await this.documentService.findAll(
      pageNumber,
      limitNumber,
      processedSearchTerm,
    );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(HttpStatus.OK, MESSAGES.DOCUMENT.FETCHED, documents, {
      totalCount,
      page: pageNumber || 'All',
      limit: limitNumber || 'All',
      totalPages,
    });
  }

  @Get(':id')
  async getDocumentById(@Param('id') id: string) {
    const document = await this.documentService.findOne(+id);
    if (!document) throw new NotFoundException(MESSAGES.DOCUMENT.NOT_FOUND);
    return createResponse(200, MESSAGES.DOCUMENT.FETCHED, document);
  }

  @Put(':id')
  async updateDocument(
    @Param('id') id: string,
    @Body() body: UpdateDocumentDto,
  ) {
    const updated = await this.documentService.update(+id, body);
    return createResponse(200, MESSAGES.DOCUMENT.UPDATED, updated);
  }

  @Delete(':id')
  async deleteDocument(@Param('id') id: string) {
    await this.documentService.delete(+id);
    return createResponse(200, MESSAGES.DOCUMENT.DELETED);
  }

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: multerS3({
        s3: new S3Client({
          region: process.env.AWS_REGION || 'ap-south-1',
          credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
          },
        }),
        bucket: 'suninfotek-docx',
        contentType: AUTO_CONTENT_TYPE,
        key: (
          req: Request,
          file: Express.Multer.File,
          cb: (error: Error | null, key: string) => void,
        ) => {
          const filename = `${file.originalname}`;
          cb(null, filename);
        },
      }) as StorageEngine,

      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(pdf|docx|doc|png|jpg|jpeg|zip)$/i)) {
          return cb(
            new Error(
              'Only PDF, DOC, DOCX, PNG, JPG, JPEG, and ZIP files are allowed!',
            ),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: S3File,
    @Body()
    body: {
      description?: string;
      userId?: number;
      process_type?: string;
      clientId?: number;
    },
  ) {
    if (!file) {
      throw new BadRequestException(MESSAGES.DOCUMENT.NOT_FOUND);
    }

    const document = await this.documentService.createDocument(
      file,
      body.description,
      body.userId ? +body.userId : undefined,
      body.process_type,
      body.clientId ? +body.clientId : undefined,
    );

    return createResponse(201, MESSAGES.DOCUMENT.UPLOADED, {
      id: document.id,
      file_name: document.file_name,
      file_type: document.file_type,
      file_path: document.file_path,
      file_url: file.location,
      description: document.description,
      process_type: document.process_type,
      userId: document.userId,
      clientId: document.clientId,
      created_at: document.created_at,
      updated_at: document.updated_at,
    });
  }

  @Put('file/:id')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: multerS3({
        s3: new S3Client({
          region: process.env.AWS_REGION || 'ap-south-1',
          credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
          },
        }),
        bucket: 'suninfotek-docx',
        contentType: AUTO_CONTENT_TYPE,
        key: (
          req: Request,
          file: Express.Multer.File,
          cb: (error: Error | null, key: string) => void,
        ) => {
          const filename = `${file.originalname}`;
          cb(null, filename);
        },
      }) as StorageEngine,

      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(pdf|docx|doc|png|jpg|jpeg|zip)$/i)) {
          return cb(
            new Error(
              'Only PDF, DOC, DOCX, PNG, JPG, JPEG, and ZIP files are allowed!',
            ),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async updateDocumentFile(
    @Param('id') id: string,
    @UploadedFile() file: S3File | undefined,
    @Body()
    body: {
      description?: string;
      userId?: string;
      process_type?: string;
      clientId?: string;
    },
  ) {
    const document = await this.documentService.findOne(+id);
    if (!document) throw new NotFoundException(MESSAGES.DOCUMENT.NOT_FOUND);

    const updates: Partial<Document> = {
      description: body.description,
      userId: body.userId ? +body.userId : undefined,
      process_type: body.process_type,
      clientId: body.clientId ? +body.clientId : undefined,
    };

    if (file) {
      updates.file_name = file.originalname;
      updates.file_path = file.location;
      updates.file_type = extname(file.originalname)
        .replace('.', '')
        .toLowerCase();
    }

    const updated = await this.documentService.update(+id, updates);
    return createResponse(200, MESSAGES.DOCUMENT.UPDATED, updated);
  }
}
