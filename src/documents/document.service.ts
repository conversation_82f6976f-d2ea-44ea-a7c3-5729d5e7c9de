import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Document } from './document.entity';
import { extname } from 'path';
import type { S3File } from './document.controller';

@Injectable()
export class DocumentService {
  constructor(
    @InjectRepository(Document)
    private documentRepository: Repository<Document>,
  ) {}

  async findAll(
    page?: number,
    limit?: number,
    searchTerm?: string,
  ): Promise<{ documents: Document[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit || undefined;

    const query = this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.user', 'user')
      .leftJoinAndSelect('document.client', 'client')
      .orderBy('document.id', 'DESC')
      .skip(skip)
      .take(take);

    if (searchTerm) {
      query.andWhere(
        `document.file_name ILIKE :search
        OR document.file_type ILIKE :search
        OR document.description ILIKE :search
        OR user.name ILIKE :search
        OR user.email ILIKE :search
        OR client.name ILIKE :search
        OR client.email ILIKE :search`,
        { search: `%${searchTerm}%` },
      );
    }

    const [documents, totalCount] = await query.getManyAndCount();

    return { documents, totalCount };
  }
  async findOne(id: number): Promise<Document | null> {
    return this.documentRepository.findOne({
      where: { id },
      relations: ['user', 'client'],
    });
  }

  async update(id: number, updates: Partial<Document>): Promise<Document> {
    const document = await this.findOne(id);
    if (!document) throw new NotFoundException('Document not found');
    Object.assign(document, updates);
    return this.documentRepository.save(document);
  }

  async delete(id: number): Promise<{ affected?: number | null }> {
    const result = await this.documentRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException('Document not found');
    }
    return result;
  }

  async createDocument(
    file: S3File,
    description?: string,
    userId?: number,
    process_type?: string,
    clientId?: number,
  ): Promise<Document> {
    const fileType = extname(file.originalname).replace('.', '').toLowerCase();

    const document = this.documentRepository.create({
      file_type: fileType,
      file_name: file.originalname,
      file_path: file.location,
      description,
      userId,
      process_type,
      clientId,
    });

    return this.documentRepository.save(document);
  }
}
