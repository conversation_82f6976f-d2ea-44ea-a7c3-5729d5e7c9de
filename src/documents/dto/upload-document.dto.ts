import { IsOptional, IsString, <PERSON>N<PERSON>ber } from 'class-validator';

export class UpdateDocumentDto {
  @IsOptional()
  @IsString()
  file_name?: string;

  @IsOptional()
  @IsString()
  file_path?: string;

  @IsOptional()
  @IsString()
  file_type?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  process_type?: string;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsOptional()
  @IsNumber()
  clientId?: number;
}
