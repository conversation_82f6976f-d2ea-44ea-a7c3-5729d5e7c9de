// src/extracted-data/extracted-data.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExtractedData } from './extracted-data.entity';
import { ExtractedDataService } from './extracted-data.service';
import { ExtractedDataController } from './extracted-data.controller';
import { Job } from '../jobs/job.entity';
import { Document } from '../documents/document.entity';
import { ExtractedDataFile } from 'src/extracteDataFiles/extracteDataFile.entity';
import { Client } from 'src/clients/client.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ExtractedData,
      Job,
      Document,
      ExtractedDataFile,
      Client,
    ]),
  ],
  providers: [ExtractedDataService],
  controllers: [ExtractedDataController],
})
export class ExtractedDataModule {}
