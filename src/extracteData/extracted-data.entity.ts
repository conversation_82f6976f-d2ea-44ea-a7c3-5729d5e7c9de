// src/extracted-data/extracted-data.entity.ts
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Job } from '../jobs/job.entity';
import { ExtractedDataFile } from 'src/extracteDataFiles/extracteDataFile.entity';

@Entity('extracted_data')
export class ExtractedData {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'job_id' })
  jobId: number;

  @Column({ type: 'jsonb' })
  data: any;

  @CreateDateColumn({ name: 'extracted_at' })
  extracted_at: Date;

  @ManyToOne(() => Job, (job) => job.extractedData, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;

  @OneToMany(() => ExtractedDataFile, (file) => file.extractedData, {
    cascade: true,
  })
  files: ExtractedDataFile[];
}
