import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  <PERSON>,
  Query,
} from '@nestjs/common';
import { ExtractedDataService } from './extracted-data.service';
import { MESSAGES } from 'src/common/utils/messageConfig';
import { createResponse } from 'src/common/utils/responseHandler';

@Controller('extracted-data')
export class ExtractedDataController {
  constructor(private readonly extractedDataService: ExtractedDataService) {}

  @Get()
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
    @Query('clientId') clientId?: string,
    @Query('startDate') startDate?: string | null,
    @Query('jobTypeId') jobTypeId?: string,
  ) {
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;
    const processedClientId =
      clientId && clientId !== 'undefined' ? Number(clientId) : undefined;

    const processedJobTypeId =
      jobTypeId && jobTypeId !== 'undefined' ? jobTypeId : undefined;

    // Validate and parse the date
    let parsedStartDate: Date | undefined = undefined;
    if (startDate && startDate !== 'null' && startDate !== 'undefined') {
      try {
        const decodedDateStr = decodeURIComponent(startDate); // Safely decode
        const date = new Date(decodedDateStr); // Parse
        if (!isNaN(date.getTime())) {
          parsedStartDate = date;
        }
      } catch {
        console.warn('Invalid startDate format:', startDate);
      }
    }
    const { data, totalCount } =
      await this.extractedDataService.findAllWithRelations(
        pageNumber,
        limitNumber,
        processedSearchTerm,
        processedClientId,
        processedJobTypeId,
        parsedStartDate,
      );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(
      HttpStatus.OK,
      MESSAGES.EXTRACTED_DATA.FETCHED,
      data,
      {
        totalCount,
        page: pageNumber || 'All',
        limit: limitNumber || 'All',
        totalPages,
      },
    );
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.extractedDataService.findById(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: { data: Record<string, any> },
  ) {
    return this.extractedDataService.update(id, updateData.data);
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    await this.extractedDataService.remove(id);
    return createResponse(HttpStatus.OK, MESSAGES.EXTRACTED_DATA.DELETED);
  }
}
