// src/extracted-data/extracted-data.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExtractedData } from './extracted-data.entity';
import { MESSAGES } from 'src/common/utils/messageConfig';
import { Document } from '../documents/document.entity';

@Injectable()
export class ExtractedDataService {
  constructor(
    @InjectRepository(ExtractedData)
    private extractedDataRepo: Repository<ExtractedData>,

    @InjectRepository(Document)
    private documentRepo: Repository<Document>,
  ) {}

  async findAllWithRelations(
    page?: number,
    limit?: number,
    searchTerm?: string,
    clientId?: number,
    jobTypeId?: string,
    startDate?: Date,
  ): Promise<{ data: ExtractedData[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit || undefined;

    const query = this.extractedDataRepo
      .createQueryBuilder('extracted')
      .leftJoinAndSelect('extracted.job', 'job')
      .leftJoinAndSelect('job.client', 'client')
      .leftJoinAndSelect('extracted.files', 'files')
      .orderBy('extracted.id', 'DESC')
      .skip(skip)
      .take(take);

    if (searchTerm) {
      query.andWhere(
        `job.job_name ILIKE :search
      OR job.status ILIKE :search
      OR document.file_name ILIKE :search
      OR extracted.data::text ILIKE :search
      OR client.name ILIKE :search`,
        { search: `%${searchTerm}%` },
      );
    }

    if (clientId) {
      query.andWhere('job.clientId = :clientId', { clientId });
    }

    if (jobTypeId) {
      query.andWhere('job.job_type = :jobTypeId', { jobTypeId });
    }

    if (startDate && !isNaN(startDate.getTime())) {
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);
      query.andWhere('extracted.extracted_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const [data, totalCount] = await query.getManyAndCount();

    for (const item of data) {
      if (item.job?.documentId?.length) {
        item.job['documents'] = await this.documentRepo.findByIds(
          item.job.documentId,
        );
      } else {
        item.job['documents'] = [];
      }
    }

    return { data, totalCount };
  }

  async findById(id: number) {
    const extractedData = await this.extractedDataRepo.findOne({
      where: { id },
      relations: ['job', 'job.client', 'files'], // Add 'job.client' relation
    });

    if (!extractedData) {
      return null;
    }

    if (extractedData.job?.documentId?.length) {
      extractedData.job['documents'] = await this.documentRepo.findByIds(
        extractedData.job.documentId,
      );
    } else {
      extractedData.job['documents'] = [];
    }

    return extractedData;
  }

  update(id: number, data: Record<string, any>) {
    return this.extractedDataRepo.update(id, { data });
  }

  async remove(id: number): Promise<void> {
    const result = await this.extractedDataRepo.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(MESSAGES.EXTRACTED_DATA.NOT_FOUND);
    }
  }
}
