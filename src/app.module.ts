import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import dataSource from './config/database.config';
import { DocumentModule } from './documents/document.module';
import { TemplateModule } from './templates/template.module';
import { UserModule } from './users/user.module';
import { JobModule } from './jobs/job.module';
import { ClientModule } from './clients/client.module';
import { ExtractedDataModule } from './extracteData/extracted-data.module';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './signIn/auth.module';
import { ExtractedDataFileModule } from './extracteDataFiles/extracteDataFile.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRoot(dataSource.options),
    DocumentModule,
    TemplateModule,
    UserModule,
    JobModule,
    ClientModule,
    ExtractedDataModule,
    AuthModule,
    ExtractedDataFileModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
