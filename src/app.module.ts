import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentModule } from './documents/document.module';
import { TemplateModule } from './templates/template.module';
import { UserModule } from './users/user.module';
import { JobModule } from './jobs/job.module';
import { ClientModule } from './clients/client.module';
import { ExtractedDataModule } from './extracteData/extracted-data.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './signIn/auth.module';
import { ExtractedDataFileModule } from './extracteDataFiles/extracteDataFile.module';
import { Document } from './documents/document.entity';
import { Template } from './templates/template.entity';
import { User } from './users/user.entity';
import { Job } from './jobs/job.entity';
import { Client } from './clients/client.entity';
import { ExtractedData } from './extracteData/extracted-data.entity';
import { ExtractedDataFile } from './extracteDataFiles/extracteDataFile.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        console.log('🔍 TypeORM Configuration Debug:');
        console.log('DB_HOST:', configService.get('DB_HOST'));
        console.log('DB_PORT:', configService.get('DB_PORT'));
        console.log('DB_USER:', configService.get('DB_USER'));
        console.log('DB_NAME:', configService.get('DB_NAME'));
        console.log('DB_PASSWORD:', configService.get('DB_PASSWORD') ? '***HIDDEN***' : 'NOT SET');

        return {
          type: 'postgres',
          host: configService.get('DB_HOST') || 'localhost',
          port: parseInt(configService.get('DB_PORT') || '5432', 10),
          username: configService.get('DB_USER') || 'postgres',
          password: configService.get('DB_PASSWORD') || '',
          database: configService.get('DB_NAME') || 'sundocx_db',
          entities: [
            Document,
            Template,
            User,
            Job,
            Client,
            ExtractedData,
            ExtractedDataFile,
          ],
          synchronize: false,
          logging: ['query', 'error'],
          ssl: false,
        };
      },
      inject: [ConfigService],
    }),
    DocumentModule,
    TemplateModule,
    UserModule,
    JobModule,
    ClientModule,
    ExtractedDataModule,
    AuthModule,
    ExtractedDataFileModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
