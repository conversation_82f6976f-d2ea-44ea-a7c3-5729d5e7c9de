import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import * as bcrypt from 'bcryptjs';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('generate-password')
  async generatePasswordHash(): Promise<string> {
    const plain = 'Admin123@';
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(plain, saltRounds);

    console.log('Generated Hashed Password:', hashedPassword);
    return hashedPassword; // Return the hashed password
  }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }
}
