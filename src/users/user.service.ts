import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { User } from './user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { hashPassword } from 'src/common/utils/bcrypt';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(
    page?: number,
    limit?: number,
    searchTerm?: string,
  ): Promise<{ users: User[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit || undefined;

    const where = searchTerm
      ? [
          { name: ILike(`%${searchTerm}%`) },
          { email: ILike(`%${searchTerm}%`) },
          { phone: ILike(`%${searchTerm}%`) },
        ]
      : {};

    const [users, totalCount] = await this.userRepository.findAndCount({
      where,
      take,
      skip,
      order: { id: 'DESC' },
    });

    return { users, totalCount };
  }

  async findOne(id: number): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  async create(createDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create({
      ...createDto,
      password: await hashPassword(createDto.password),
    });
    return this.userRepository.save(user);
  }

  async update(id: number, updateDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);
    if (!user) throw new NotFoundException('User not found');
    if (updateDto.password) {
      updateDto.password = await hashPassword(updateDto.password);
    }
    Object.assign(user, updateDto);
    return this.userRepository.save(user);
  }

  async delete(id: number): Promise<{ affected?: number | null }> {
    const result = await this.userRepository.delete(id);
    if (result.affected === 0) throw new NotFoundException('User not found');
    return result;
  }
}
