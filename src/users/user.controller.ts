import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { createResponse } from '../common/utils/responseHandler';
import { MESSAGES } from '../common/utils/messageConfig';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async getAllUsers(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    // Handle "undefined" string case
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;

    const { users, totalCount } = await this.userService.findAll(
      pageNumber,
      limitNumber,
      processedSearchTerm,
    );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(200, MESSAGES.USER.FETCHED, users, {
      totalCount,
      page: pageNumber || 'All',
      limit: limitNumber || 'All',
      totalPages,
    });
  }

  @Get(':id')
  async getUserById(@Param('id') id: string) {
    const user = await this.userService.findOne(+id);
    if (!user) throw new NotFoundException(MESSAGES.USER.NOT_FOUND);
    return createResponse(200, MESSAGES.USER.FETCHED, user);
  }

  @Post()
  async createUser(@Body() body: CreateUserDto) {
    const user = await this.userService.create(body);
    return createResponse(201, MESSAGES.USER.CREATED, user);
  }

  @Put(':id')
  async updateUser(@Param('id') id: string, @Body() body: UpdateUserDto) {
    const user = await this.userService.update(+id, body);
    return createResponse(200, MESSAGES.USER.UPDATED, user);
  }

  @Delete(':id')
  async deleteUser(@Param('id') id: string) {
    await this.userService.delete(+id);
    return createResponse(200, MESSAGES.USER.DELETED);
  }
}
