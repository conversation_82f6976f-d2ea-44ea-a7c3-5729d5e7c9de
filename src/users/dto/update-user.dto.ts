import {
  IsOptional,
  IsString,
  IsBoolean,
  IsEmail,
  Length,
  Matches,
} from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsString()
  @Length(10, 10, { message: 'Please enter a valid mobile number.' })
  @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
  phone!: string;
}
