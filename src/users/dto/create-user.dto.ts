import { IsEmail, IsString, Length, Matches, MinLength } from 'class-validator';

export class CreateUserDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsString()
  @Length(10, 10, { message: 'Please enter a valid mobile number.' })
  @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
  phone!: string;
}
