// src/extracted-data-files/extracted-data-file.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
} from 'typeorm';

import { Job } from '../jobs/job.entity';
import { ExtractedData } from 'src/extracteData/extracted-data.entity';

@Entity('extracted_data_files')
export class ExtractedDataFile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'extracted_data_id' })
  extractedDataId: number;

  @Column({ name: 'job_id' })
  jobId: number;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ type: 'jsonb' })
  data: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => ExtractedData, (extractedData) => extractedData.files, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'extracted_data_id' })
  extractedData: ExtractedData;

  @ManyToOne(() => Job, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_id' })
  job: Job;
}
