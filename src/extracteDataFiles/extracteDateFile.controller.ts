// src/extracted-data-files/extracted-data-file.controller.ts
import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';

import { createResponse } from 'src/common/utils/responseHandler';
import { MESSAGES } from 'src/common/utils/messageConfig';
import { ExtractedDataFileService } from './extracteDataFiles.service';

@Controller('extracted-data-files')
export class ExtractedDataFileController {
  constructor(private readonly fileService: ExtractedDataFileService) {}

  @Get('by-extracted/:extractedDataId')
  async findAllByExtractedDataId(
    @Param('extractedDataId', ParseIntPipe) extractedDataId: number,
  ) {
    const { data, totalCount } =
      await this.fileService.findAllWithRelations(extractedDataId);

    return createResponse(
      HttpStatus.OK,
      MESSAGES.EXTRACTED_DATA.FETCHED,
      data,
      { totalCount },
    );
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const data = await this.fileService.findById(id);
    return createResponse(HttpStatus.OK, MESSAGES.EXTRACTED_DATA.FETCHED, data);
  }
}
