// src/extracted-data-files/extracted-data-file.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Document } from '../documents/document.entity';
import { ExtractedDataFile } from './extracteDataFile.entity';

@Injectable()
export class ExtractedDataFileService {
  constructor(
    @InjectRepository(ExtractedDataFile)
    private readonly extractedDataFileRepo: Repository<ExtractedDataFile>,

    @InjectRepository(Document)
    private readonly documentRepo: Repository<Document>,
  ) {}

  async findAllWithRelations(
    extractedDataId: number,
  ): Promise<{ data: ExtractedDataFile[]; totalCount: number }> {
    const [data, totalCount] = await this.extractedDataFileRepo.findAndCount({
      where: { extractedDataId },
      relations: ['job'],
      order: { id: 'DESC' },
    });

    for (const item of data) {
      if (item.job?.documentId?.length) {
        item.job['documents'] = await this.documentRepo.findByIds(
          item.job.documentId,
        );
      } else {
        item.job['documents'] = [];
      }
    }

    return { data, totalCount };
  }

  async findById(id: number): Promise<ExtractedDataFile | null> {
    const file = await this.extractedDataFileRepo.findOne({
      where: { id },
      relations: ['job'],
    });

    if (!file) return null;

    if (file.job?.documentId?.length) {
      file.job['documents'] = await this.documentRepo.findByIds(
        file.job.documentId,
      );
    } else {
      file.job['documents'] = [];
    }

    return file;
  }
}
