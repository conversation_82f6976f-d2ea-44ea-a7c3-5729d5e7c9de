// src/extracted-data-files/extracted-data-file.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Document } from '../documents/document.entity';
import { ExtractedDataFile } from './extracteDataFile.entity';
import { ExtractedDataFileService } from './extracteDataFiles.service';
import { ExtractedDataFileController } from './extracteDateFile.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ExtractedDataFile, Document])],
  providers: [ExtractedDataFileService],
  controllers: [ExtractedDataFileController],
})
export class ExtractedDataFileModule {}
