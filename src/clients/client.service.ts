import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Client } from './client.entity';
import { CreateClientDto, UpdateClientDto } from './dto/create-client.dto';
import { User } from 'src/users/user.entity';

@Injectable()
export class ClientService {
  constructor(
    @InjectRepository(Client)
    private clientRepository: Repository<Client>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(
    page?: number,
    limit?: number,
    searchTerm?: string,
  ): Promise<{ clients: Client[]; totalCount: number }> {
    const skip = page && limit ? (page - 1) * limit : undefined;
    const take = limit || undefined;

    const query = this.clientRepository
      .createQueryBuilder('client')
      .leftJoinAndSelect('client.created_by', 'created_by')
      .leftJoinAndSelect('client.jobs', 'jobs')
      .orderBy('client.id', 'DESC')
      .skip(skip)
      .take(take);

    if (searchTerm) {
      query.andWhere(
        `client.name ILIKE :search
        OR client.email ILIKE :search
        OR client.phone ILIKE :search
        OR client.address ILIKE :search
        OR created_by.name ILIKE :search
        OR created_by.email ILIKE :search`,
        { search: `%${searchTerm}%` },
      );
    }

    const [clients, totalCount] = await query.getManyAndCount();

    return { clients, totalCount };
  }

  async findOne(id: number): Promise<Client> {
    const client = await this.clientRepository.findOne({
      where: { id },
      relations: ['created_by', 'jobs'],
    });
    if (!client) throw new NotFoundException('Client not found');
    return client;
  }

  async create(createDto: CreateClientDto): Promise<Client> {
    const { created_by, ...rest } = createDto;

    const user = await this.userRepository.findOne({
      where: { id: created_by },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${created_by} not found`);
    }

    const newClient = this.clientRepository.create({
      ...rest,
      created_by: user,
    });

    return this.clientRepository.save(newClient);
  }
  async update(id: number, updateDto: UpdateClientDto): Promise<Client> {
    const client = await this.clientRepository.findOneBy({ id });
    if (!client) throw new NotFoundException('Client not found');

    if (updateDto.created_by) {
      const user = await this.userRepository.findOneBy({
        id: updateDto.created_by,
      });
      if (!user) throw new NotFoundException('User not found');
      client.created_by = user;
    }

    client.name = updateDto.name ?? client.name;
    client.description = updateDto.description ?? client.description;
    client.email = updateDto.email ?? client.email;
    client.phone = updateDto.phone ?? client.phone;
    client.address = updateDto.address ?? client.address;
    client.status = updateDto.status ?? client.status;

    return this.clientRepository.save(client);
  }

  async delete(id: number): Promise<void> {
    const result = await this.clientRepository.delete(id);
    if (result.affected === 0) throw new NotFoundException('Client not found');
  }
}
