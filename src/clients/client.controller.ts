import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
} from '@nestjs/common';
import { ClientService } from './client.service';
import { CreateClientDto, UpdateClientDto } from './dto/create-client.dto';

import { createResponse } from '../common/utils/responseHandler';
import { MESSAGES } from '../common/utils/messageConfig';

@Controller('clients')
export class ClientController {
  constructor(private readonly clientService: ClientService) {}

  @Get()
  async getAllClients(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    const pageNumber = page && page !== 'undefined' ? Number(page) : undefined;
    const limitNumber =
      limit && limit !== 'undefined' ? Number(limit) : undefined;
    const processedSearchTerm =
      searchTerm && searchTerm !== 'undefined' ? searchTerm : undefined;

    const { clients, totalCount } = await this.clientService.findAll(
      pageNumber,
      limitNumber,
      processedSearchTerm,
    );

    const totalPages = limitNumber ? Math.ceil(totalCount / limitNumber) : 1;

    return createResponse(200, MESSAGES.CLIENT.FETCHED, clients, {
      totalCount,
      page: pageNumber || 'All',
      limit: limitNumber || 'All',
      totalPages,
    });
  }

  @Get(':id')
  async getClientById(@Param('id') id: string) {
    const client = await this.clientService.findOne(+id);
    return createResponse(200, MESSAGES.CLIENT.FETCHED, client);
  }

  @Post()
  async createClient(@Body() body: CreateClientDto) {
    const client = await this.clientService.create(body);
    return createResponse(201, MESSAGES.CLIENT.CREATED, client);
  }

  @Put(':id')
  async updateClient(@Param('id') id: string, @Body() body: UpdateClientDto) {
    const client = await this.clientService.update(+id, body);
    return createResponse(200, MESSAGES.CLIENT.UPDATED, client);
  }

  @Delete(':id')
  async deleteClient(@Param('id') id: string) {
    await this.clientService.delete(+id);
    return createResponse(200, MESSAGES.CLIENT.DELETED);
  }
}
