import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Client } from './client.entity';
import { ClientService } from './client.service';
import { ClientController } from './client.controller';
import { User } from 'src/users/user.entity';
import { Job } from 'src/jobs/job.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Client, User, Job])],
  controllers: [ClientController],
  providers: [ClientService],
  exports: [ClientService],
})
export class ClientModule {}
