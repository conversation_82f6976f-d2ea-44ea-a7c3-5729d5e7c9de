#!/usr/bin/env node

// Debug script to check environment variables
require('dotenv').config();

console.log('='.repeat(50));
console.log('🔍 ENVIRONMENT VARIABLES DEBUG');
console.log('='.repeat(50));

console.log('📁 Current working directory:', process.cwd());
console.log('📄 .env file exists:', require('fs').existsSync('.env'));

if (require('fs').existsSync('.env')) {
  console.log('📄 .env file contents:');
  console.log(require('fs').readFileSync('.env', 'utf8'));
}

console.log('\n🌍 Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'NOT SET');
console.log('PORT:', process.env.PORT || 'NOT SET');
console.log('DB_HOST:', process.env.DB_HOST || 'NOT SET');
console.log('DB_PORT:', process.env.DB_PORT || 'NOT SET');
console.log('DB_USER:', process.env.DB_USER || 'NOT SET');
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***HIDDEN***' : 'NOT SET');
console.log('DB_NAME:', process.env.DB_NAME || 'NOT SET');
console.log('AWS_REGION:', process.env.AWS_REGION || 'NOT SET');
console.log('AWS_ACCESS_KEY_ID:', process.env.AWS_ACCESS_KEY_ID ? '***HIDDEN***' : 'NOT SET');
console.log('AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? '***HIDDEN***' : 'NOT SET');

console.log('\n🔧 Database Configuration Test:');
const dbConfig = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sundocx_db',
};

console.log('Final DB Config:', {
  ...dbConfig,
  password: dbConfig.password ? '***HIDDEN***' : 'NOT SET'
});

console.log('='.repeat(50));
