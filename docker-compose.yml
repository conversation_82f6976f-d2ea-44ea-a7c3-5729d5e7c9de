version: '3.8'

services:
  # NestJS Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      # Add your environment variables here
      # - DATABASE_HOST=postgres
      # - DATABASE_PORT=5432
      # - DATABASE_USERNAME=your_username
      # - DATABASE_PASSWORD=your_password
      # - DATABASE_NAME=your_database
      # - AWS_ACCESS_KEY_ID=your_access_key
      # - AWS_SECRET_ACCESS_KEY=your_secret_key
      # - AWS_REGION=your_region
    volumes:
      # Mount AWS credentials if needed (optional)
      # - ~/.aws:/home/<USER>/.aws:ro
      # Mount any additional volumes as needed
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - mysql
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL Database (optional - uncomment if needed)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sundocx
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - app-network

  # MySQL Database (optional - uncomment if needed)
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: sundocx
      MYSQL_USER: mysql
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
  mysql_data:

networks:
  app-network:
    driver: bridge
