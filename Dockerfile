# Use Ubuntu 22.04 LTS as base image
FROM ubuntu:22.04

# Set environment variables
ENV NODE_VERSION=20.x
ENV DEBIAN_FRONTEND=noninteractive
ENV PORT=3000

# Set working directory
WORKDIR /app

# Update package list and install essential packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    lsb-release \
    ca-certificates \
    software-properties-common \
    build-essential \
    python3 \
    python3-pip \
    unzip \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20.x
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf awscliv2.zip aws/

# Verify installations
RUN node --version && npm --version && aws --version

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install

# Copy source code and .env file
COPY . .

# Make debug script executable
RUN chmod +x debug-env.js

# Debug environment variables during build
RUN node debug-env.js

# Expose port
EXPOSE 3000

# Start the application with explicit environment loading
CMD ["sh", "-c", "node debug-env.js && npm start"]
