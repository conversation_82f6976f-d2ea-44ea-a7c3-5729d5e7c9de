service: sundocx-backend

frameworkVersion: '3'
provider:
  name: aws
  region: ${opt:stage, 'ap-south-1'}
  stage: ${opt:stage, 'dev'}
  deploymentBucket:
    name: ${self:custom.deploymentBucketName}
  
custom:
  prefix: ${param:PREFIX}
  deploymentBucketName: ${param:PIPELINE_ARTIFACT_BUCKET_NAME}
  backendEcrRepositoryUri: ${param:BACKEND_ECR_REPOSITORY_URI}
  imageTag: ${param:IMAGE_TAG}
  vpcId: ${param:VPC_ID}
  publicsubnet1: ${param:PUBLIC_SUBNET_1}
  publicsubnet2: ${param:PUBLIC_SUBNET_2}
  ## Uncomment the following line if you want to use HTTPS for the ALB
  # BackendDomainCertificateArn: ${param:BACKEND_DOMAIN_CERTIFICATE_ARN}

resources:
  Resources:
    EcsClusterTaskRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-task-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ECS-Task-Execution-Policy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchCheckLayerAvailability
                    - ecr:GetDownloadUrlForLayer
                    - ecr:BatchGetImage
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - logs:DescribeLogStreams
                    - logs:DescribeLogGroups
                  Resource: '*'
    #Security Group for ALB
    ALBSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow traffic to ALB
        VpcId: ${self:custom.vpcId}
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            CidrIp: 0.0.0.0/0
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 80
            CidrIp: 0.0.0.0/0

    LoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: !Sub ${self:custom.prefix}-${self:provider.stage}-alb
        Subnets:
          - ${self:custom.publicsubnet1}
          - ${self:custom.publicsubnet2}
        SecurityGroups:
          - !Ref ALBSecurityGroup
        Scheme: internet-facing
        LoadBalancerAttributes:
          - Key: idle_timeout.timeout_seconds
            Value: '60'
    
    TargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      Properties:
        Name: !Sub ${self:custom.prefix}-${self:provider.stage}-tg
        VpcId: ${self:custom.vpcId}
        Protocol: HTTP
        Port: 3000
        HealthCheckProtocol: HTTP
        HealthCheckPath: /
        Matcher:
          HttpCode: 200
        TargetType: ip
  
    ALBListenerHTTP:
      Type: AWS::ElasticLoadBalancingV2::Listener
      Properties:
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref TargetGroup
        LoadBalancerArn: !Ref LoadBalancer
        Port: 80
        Protocol: HTTP

    # ALBListenerHTTPS:
    #   Type: AWS::ElasticLoadBalancingV2::Listener
    #   Properties:
    #     DefaultActions:
    #       - Type: forward
    #         TargetGroupArn: !Ref TargetGroup
    #     LoadBalancerArn: !Ref LoadBalancer
    #     Port: 443
    #     Protocol: HTTPS
    #     Certificates:
    #       - CertificateArn: !Sub ${self:custom.BackendDomainCertificateArn}

    ECSSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow traffic to ECS
        VpcId: ${self:custom.vpcId}
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 3000
            ToPort: 3000
            SourceSecurityGroupId: !Ref ALBSecurityGroup

    #Resource for ECS
    ECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-execution-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - ecs-tasks.amazonaws.com
              Action:
                - sts:AssumeRole
        Policies:
          - PolicyName: ECRAccess
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchCheckLayerAvailability
                    - ecr:GetDownloadUrlForLayer
                    - ecr:BatchGetImage
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - logs:DescribeLogStreams
                    - logs:DescribeLogGroups
                  Resource: '*'

    TaskExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-task-execution-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - ecs-tasks.amazonaws.com
              Action:
                - sts:AssumeRole
        Policies:
            - PolicyName: CloudWatchLogsPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - logs:*
                    Resource: "*"
            - PolicyName: S3BucketAccessPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - s3:GetBucketLocation
                      - s3:ListBucket
                      - s3:GetObject
                      - s3:PutObject
                      - s3:GetBucketAcl
                      - s3:GetObjectVersion
                      - s3:DeleteObject
                      - s3:DeleteObjectVersion
                      - s3:DeleteObjectTagging
                      - s3:DeleteObjectVersionTagging
                      - s3:PutObjectTagging
                      - s3:PutObjectVersionTagging
                      - s3:RestoreObject
                      - s3:GetObjectTagging
                      - s3:GetObjectVersionTagging
                      - s3:ListMultipartUploadParts
                      - s3:AbortMultipartUpload
                      - s3:ListBucketMultipartUploads
                      - s3:ListObjectsV2
                    Resource: "*"

    BackendTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      Properties:
        Family: !Sub ${self:custom.prefix}-${self:provider.stage}-task-definition
        RequiresCompatibilities:
          - FARGATE
        NetworkMode: awsvpc
        Cpu: 1024
        Memory: 2048 
        EphemeralStorage:
          SizeInGiB: 21
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        ExecutionRoleArn: !GetAtt ECSExecutionRole.Arn
        TaskRoleArn: !GetAtt TaskExecutionRole.Arn
        ContainerDefinitions:
          - Name: !Sub ${self:custom.prefix}-${self:provider.stage}-container
            Image: !Sub ${self:custom.backendEcrRepositoryUri}:${self:custom.imageTag}
            PortMappings:
              - Name: "nestjs_server_port"
                ContainerPort: 3000
                HostPort: 3000
                Protocol: tcp
                AppProtocol: http
            Essential: true
            Environment:
              - Name: NODE_ENV
                Value: !Sub ${self:provider.stage}
              - Name: AWS_REGION
                Value: !Ref AWS::Region
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: !Sub /ecs/${self:custom.prefix}-${self:provider.stage}-logs
                awslogs-create-group: "true"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: ecs
    
    BackendECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: !Sub ${self:custom.prefix}-${self:provider.stage}-ecs-cluster

    BackendECSService:
      Type: AWS::ECS::Service
      DependsOn: ALBListenerHTTP
      Properties:
        Cluster: !Ref BackendECSCluster
        ServiceName: !Sub ${self:custom.prefix}-${self:provider.stage}-service
        TaskDefinition: !Ref BackendTaskDefinition
        LaunchType: FARGATE
        DesiredCount: 1
        NetworkConfiguration:
          AwsvpcConfiguration:
            Subnets:
              - ${self:custom.publicsubnet1}
              - ${self:custom.publicsubnet2}
            SecurityGroups:
              - !Ref ECSSecurityGroup
            AssignPublicIp: ENABLED
        LoadBalancers:
          - ContainerName: !Sub ${self:custom.prefix}-${self:provider.stage}-container
            ContainerPort: 3000
            TargetGroupArn: !Ref TargetGroup