# Env variables for the dev environment pipeline
# This file is sourced by the deploy.sh script to set environment-specific variables during pipeline deployment.   
export Stage="dev"
export Prefix="sundocx"
export Environment="dev"
export XrayEnabled="true"
export Region="ap-south-1"
export GitHubOwner="Meyi-Cloud"
export ManualApproval="false"
export GitBranch="development"
export BackendResourceType="backend"
export BackendRepositoryName="${Prefix}-backend"
export Tags="Project=${Prefix}, Environment=dev"
export PipelineStackName="${GitHubRepositoryName}-${Environment}-pipeline"
export PipelineArtifactBucket="${Prefix}-${Environment}-pipeline-artifacts"
export CodeConnectionsArn="arn:aws:codeconnections:ap-south-1:505361047739:connection/bdc57992-55c4-4c78-ac1f-ab925db0d46e"
export BackendDomainCertificateArn=""


# Network details
export VpcId="vpc-016f9cba249ffc4cd"
export PublicSubnet1="subnet-04c17a4ba7596e516"
export PublicSubnet2="subnet-04986c6006f2b82b4"
export PrivateSubnet1="subnet-0ff01ee3f84df4a16"
export PrivateSubnet2="subnet-0e39868e881cf6c83"