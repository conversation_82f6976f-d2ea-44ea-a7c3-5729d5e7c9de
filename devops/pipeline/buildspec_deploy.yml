version: 0.2
env:
  git-credential-helper: yes
phases:
  install:
    runtime-versions:
      nodejs: 20
  pre_build:
    commands:
      - pwd
      - ls -al
      - npm install
      - ls -al
      - IMAGE_TAG=$(aws ecr describe-images --repository-name $BACKEND_ECR_REPOSITORY_NAME --region $REGION --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[0]' --output text)
  build:
    commands:
      - npm install --save-dev serverless@3.38.0
      - npx serverless --version
      - npx serverless deploy --region $REGION --stage $ENV_NAME --param="PREFIX=$PREFIX"
                                                              --param="VPC_ID=$VPC_ID"
                                                              --param="IMAGE_TAG=$IMAGE_TAG"
                                                              --param="PUBLIC_SUBNET_1=$PUBLIC_SUBNET_1"
                                                              --param="PUBLIC_SUBNET_2=$PUBLIC_SUBNET_2"
                                                              --param="BACKEND_ECR_REPOSITORY_URI=$BACKEND_ECR_REPOSITORY_URI"
                                                              --param="PIPELINE_ARTIFACT_BUCKET_NAME=$PIPELINE_ARTIFACT_BUCKET_NAME"
                                                              # --param="BACKEND_DOMAIN_CERTIFICATE_ARN=$BACKEND_DOMAIN_CERTIFICATE_ARN"
      - pwd
      - ls -al
artifacts:
  files:
    - '**/**'