AWSTemplateFormatVersion : '2010-09-09'
Description: >
  CodePipeline CFN Stack for Sundocx backend.
  This stack creates a CodePipeline that builds and deploys the application.
  The pipeline is triggered by changes to the source code in the GitHub repository.
  The pipeline consists of the following stages:
  - Source: Retrieves the source code from the GitHub repository.
  - UnitTest: Runs unit tests on the source code using CodeBuild.
  - ManualApproval: Manual approval before deploying to production.
  - BuildAndPackage: Builds and packages the application using CodeBuild.
  - CodeDeploy: Deploys the application using CodeBuild.
  The pipeline uses the following resources:
  - S3: Stores the pipeline artifacts.
  - CodeBuild: Builds and tests the application.
  - CodePipeline: Orchestrates the pipeline stages.
  The pipeline uses the following IAM roles:
  - CodePipelineExecutionRole: Role assumed by CodePipeline to execute the pipeline.
  - CodeBuildServiceRole: Role assumed by CodeBuild to build and test the application.

Parameters:
  Prefix:
    Type: String
    Default: 'sundocx'
  Environment:
    Type: String
  GitHubOwner:
    Type: String
    Default: ''
  GitHubRepositoryName:
    Type: String
    Default: 'sundocx-backend'
  ResourceType:
    Type: String
    Default: 'backend'
  GitBranch:
    Type: String
    Default: 'dev'
  PipelineArtifactBucket:
    Type: String
  ManualApproval:
    Type: String
    Default: true
  Region:
    Type: String
    Default: 'ap-south-1'
  CodeConnectionsArn:
    Type: String
  XrayEnabled:
    Type: String
    Default: 'true'
  Tags:
    Type: String
    Default: 'Project=sundocx ResourceType=Backend'
  VpcId:
    Type: String
  PublicSubnet1:
    Type: String
  PublicSubnet2:
    Type: String
  PrivateSubnet1:
    Type: String
  PrivateSubnet2:
    Type: String
  ArtifactBucketExists:
    Type: String
    Default: false
  ## Uncomment the following line if you want to use HTTPS for the ALB
  # BackendDomainCertificateArn:
  #   Type: String
    
Conditions:
  IsManualApproval: !Equals [!Ref ManualApproval, "true"]
  IsArtifactBucketExists: !Equals [!Ref ArtifactBucketExists, "false"]

Resources:
  #  ____  _            _ _
  # |  _ \(_)_ __   ___| (_)_ __   ___
  # | |_) | | '_ \ / _ | | | '_ \ / _ \
  # |  __/| | |_) |  __| | | | | |  __/
  # |_|   |_| .__/ \___|_|_|_| |_|\___|
  #         |_|
  CPCBS3Bucket:
    Type: AWS::S3::Bucket
    Condition: IsArtifactBucketExists
    Properties:
      BucketName: !Ref PipelineArtifactBucket
  BackendECRRepository:
      Type: AWS::ECR::Repository
      Properties:
        RepositoryName: !Sub "${Prefix}-${Environment}-${ResourceType}-repository"
        LifecyclePolicy:
          LifecyclePolicyText: |
            {
              "rules": [
                {
                  "rulePriority": 1,
                  "description": "Retain only the latest 3 images",
                  "selection": {
                    "tagStatus": "any",
                    "countType": "imageCountMoreThan",
                    "countNumber": 3
                  },
                  "action": {
                    "type": "expire"
                  }
                }
              ]
            }

  Pipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      Name: !Sub '${Prefix}-${Environment}-${ResourceType}-pipeline'
      ArtifactStore:
        Location: !Sub ${PipelineArtifactBucket}
        Type: S3
      RoleArn: !GetAtt CodePipelineExecutionRole.Arn
      RestartExecutionOnUpdate: true
      Stages:
        - Name: Source
          Actions:
            - Name: SourceCodeRepo
              ActionTypeId:
                Category: Source
                Owner: AWS
                Provider: CodeStarSourceConnection
                Version: "1"
              Configuration:
                ConnectionArn: !Ref CodeConnectionsArn
                FullRepositoryId: !Sub "${GitHubOwner}/${GitHubRepositoryName}"
                BranchName: !Ref GitBranch
                OutputArtifactFormat: "CODE_ZIP"
              OutputArtifacts:
                  - Name: SourceCodeAsZip
              RunOrder: 1
        - Name: UnitTest
          Actions:
            - Name: UnitTest
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: '1'
              RunOrder: 1
              Configuration:
                  ProjectName: !Ref UnitTestCodeBuild
              InputArtifacts:
                  - Name: SourceCodeAsZip
              OutputArtifacts:
                  - Name: !Sub ${AWS::StackName}coverage
        - !If
          - IsManualApproval
          - Name: Approval
            Actions:
              - Name: ManualApproval
                ActionTypeId:
                  Category: Approval
                  Owner: AWS
                  Provider: Manual
                  Version: "1"
                RunOrder: 1
          - !Ref AWS::NoValue        
        - Name: BuildAndPackage
          Actions:
            - Name: CodeBuild
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: '1'
              RunOrder: 1
              Configuration:
                ProjectName: !Ref BuildPackageCodeBuild
              InputArtifacts:
                - Name: SourceCodeAsZip
              OutputArtifacts:
                - Name: BuildArtifactAsZip
        - Name: CodeDeploy
          Actions:
            - Name: Deploy
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: '1'
              Configuration:
                ProjectName: !Ref DeployCodeBuild
              InputArtifacts:
                - Name: BuildArtifactAsZip
              RunOrder: 1
  PipelineArtifactsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Sub ${PipelineArtifactBucket}
      PolicyDocument:
        Statement:
          - Effect: "Deny"
            Action: "s3:*"
            Principal: "*"
            Resource:
              - !Sub arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}
              - !Sub arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}/*
            Condition:
              Bool:
                aws:SecureTransport: false
          - Sid: AllowedtoRole
            Effect: Allow
            Principal:
              AWS:
              - !GetAtt CodePipelineExecutionRole.Arn
            Action:
              - s3:*
            Resource:
              - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}"
              - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}/*"

  CodePipelineExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${Prefix}-${Environment}-${ResourceType}-CodePipelineExecutionRole
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "sts:AssumeRole"
            Effect: Allow
            Principal:
              Service:
                - codepipeline.amazonaws.com
      Policies:
        - PolicyName: CodePipelineAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "iam:PassRole"
                Resource: "*"
        - PolicyName: CodePipelineCodeAndS3Bucket
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Action:
                  - s3:GetBucketAcl
                  - s3:GetBucketLocation
                Effect: Allow
                Resource: 
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}"
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}/*"
              - Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                  - "s3:PutObject"
                Effect: Allow
                Resource: 
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}"
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}/*"
        - PolicyName: CodePipelineCodeBuildAndCloudformationAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "codebuild:StartBuild"
                  - "codebuild:BatchGetBuilds"
                Resource:
                  - !GetAtt UnitTestCodeBuild.Arn
                  - !GetAtt DeployCodeBuild.Arn
                  - !GetAtt BuildPackageCodeBuild.Arn
              - Effect: Allow
                Action:
                  - "cloudformation:CreateStack"
                  - "cloudformation:DescribeStacks"
                  - "cloudformation:DeleteStack"
                  - "cloudformation:UpdateStack"
                  - "cloudformation:CreateChangeSet"
                  - "cloudformation:ExecuteChangeSet"
                  - "cloudformation:DeleteChangeSet"
                  - "cloudformation:DescribeChangeSet"
                  - "cloudformation:SetStackPolicy"
                  - "cloudformation:SetStackPolicy"
                  - "cloudformation:ValidateTemplate"
                Resource:
                  - !Sub "arn:${AWS::Partition}:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/${AWS::StackName}/*"
        - PolicyName: CodeStarConnectionAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'codestar-connections:UseConnection'
                  - 'codestar-connections:GetConnection'
                  - 'codestar-connections:PassConnection'
                  - 'codestar-connections:ListConnections'
                Resource: !Ref CodeConnectionsArn

  #   ____          _      ____        _ _     _
  #  / ___|___   __| | ___| __ ) _   _(_| | __| |
  # | |   / _ \ / _` |/ _ |  _ \| | | | | |/ _` |
  # | |__| (_) | (_| |  __| |_) | |_| | | | (_| |
  #  \____\___/ \__,_|\___|____/ \__,_|_|_|\__,_|
  CodeBuildServiceRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${Prefix}-${Environment}-${ResourceType}-CodeBuildServiceRole
      Tags:
        - Key: Role
          Value: aws-sam-pipeline-codebuild-service-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "sts:AssumeRole"
            Effect: Allow
            Principal:
              Service:
                - codebuild.amazonaws.com
      Policies:
        - PolicyName: CodeBuildLogs
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "logs:CreateLogGroup"
                  - "logs:CreateLogStream"
                  - "logs:PutLogEvents"
                Resource:
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/*"
        - PolicyName: CodeBuildArtifactsBucket
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "s3:GetObject"
                  - "s3:GetObjectVersion"
                  - "s3:PutObject"
                  - "s3:GetBucketLocation"
                  - "s3:ListBucket"
                Resource:
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}"
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactBucket}/*"
        - PolicyName: S3FullAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - "s3:*"
                Resource: "*"
        - PolicyName: AssumeStagePipExecutionRoles
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Action:
                  - sts:AssumeRole
                Effect: Allow
                Resource: "*"
        - PolicyName: CloudwatchLogsAccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "logs:TagLogGroup"
                  - "logs:DescribeLogGroups"
                  - "logs:DeleteLogGroup"
                  - "logs:CreateLogGroup"
                  - "logs:PutRetentionPolicy"
                  - "logs:DeleteRetentionPolicy"
                  - "logs:ListTagsLogGroup"
                  - "cloudwatch:PutMetricAlarm"
                  - "cloudwatch:PutAnomalyDetector"
                  - "cloudwatch:PutCompositeAlarm"
                  - "cloudwatch:PutInsightRule"
                  - "cloudwatch:PutManagedInsightRules"
                  - "cloudwatch:PutMetricData"
                  - "cloudwatch:PutMetricStream"
                  - "cloudwatch:DeleteAlarms"
                Effect: Allow
                Resource: '*'
        - PolicyName: CloudFormationAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "cloudformation:*"
                Effect: Allow
                Resource: '*'
        - PolicyName: IAMAccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "iam:GetRole"
                  - "iam:TagRole"
                  - "iam:ListRoles"
                  - "iam:CreateRole"
                  - "iam:AttachRolePolicy"
                  - "iam:PutRolePolicy"
                  - "iam:PassRole"
                  - "iam:DetachRolePolicy"
                  - "iam:ListAttachedRolePolicies"
                  - "iam:DeleteRolePolicy"
                  - "iam:UpdateRole"
                  - "iam:ListRolePolicies"
                  - "iam:GetRolePolicy"
                  - "iam:DeleteRole"
                  - "iam:CreateServiceLinkedRole"
                Effect: Allow
                Resource: '*'
        - PolicyName: SecretManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "secretsmanager:UntagResource"
                  - "secretsmanager:PutSecretValue"
                  - "secretsmanager:CreateSecret"
                  - "secretsmanager:DeleteSecret"
                  - "secretsmanager:TagResource"
                  - "secretsmanager:UpdateSecret"
                  - "secretsmanager:DescribeSecret"
                  - "secretsmanager:GetRandomPassword"
                  - "secretsmanager:GetSecretValue"
                  -  "s3:PutBucketPublicAccessBlock"
                Effect: Allow
                Resource: '*'
        - PolicyName: EcrAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "ecr:GetAuthorizationToken"
                  - "ecr:BatchCheckLayerAvailability"
                  - "ecr:CompleteLayerUpload"
                  - "ecr:InitiateLayerUpload"
                  - "ecr:PutImage"
                  - "ecr:DescribeRepositories"
                  - "ecr:CreateRepository"
                  - "ecr:DescribeImages"
                  - "ecr:ListImages"
                  - "ecr:BatchGetImage"
                  - "ecr:UploadLayerPart"
                Effect: Allow
                Resource: "*"
        - PolicyName: Ec2Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "ec2:DescribeSecurityGroups"
                  - "ec2:CreateSecurityGroup"
                  - "ec2:DescribeSecurityGroupRules"
                  - "ec2:AuthorizeSecurityGroupIngress"
                  - "ec2:RevokeSecurityGroupIngress"
                  - "ec2:RevokeSecurityGroupEgress"
                  - "ec2:AuthorizeSecurityGroupEgress"
                  - "ec2:DescribeSubnets"
                  - "ec2:DescribeVpcs"
                  - "ec2:DescribeRouteTables"
                  - "ec2:DescribeNetworkInterfaces"
                  - "ec2:CreateNetworkInterface"
                  - "ec2:AttachNetworkInterface"
                  - "ec2:ModifyNetworkInterfaceAttribute"
                  - "ec2:DescribeNetworkInterfaceAttribute"
                  - "ec2:DetachNetworkInterface"
                  - "ec2:CreateTags"
                  - "ec2:*"
                Effect: Allow
                Resource: "*"
        - PolicyName: AlbAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "elasticloadbalancing:DescribeLoadBalancers"
                  - "elasticloadbalancing:DescribeTargetGroups"
                  - "elasticloadbalancing:DescribeTargetHealth"
                  - "elasticloadbalancing:RegisterTargets"
                  - "elasticloadbalancing:DeregisterTargets"
                  - "elasticloadbalancing:createTargetGroup"
                  - "elasticloadbalancing:CreateListener"
                  - "elasticloadbalancing:CreateRule"
                  - "elasticloadbalancing:ModifyListener"
                  - "elasticloadbalancing:ModifyRule"
                  - "elasticloadbalancing:CreateLoadBalancer"
                  - "elasticloadbalancing:ModifyLoadBalancerAttributes"
                  - "elasticloadbalancing:*"
                Effect: Allow
                Resource: "*"
        - PolicyName: EcsAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Action:
                  - "ecs:ListClusters"
                  - "ecs:DescribeClusters"
                  - "ecs:ListServices"
                  - "ecs:DescribeServices"
                  - "ecs:UpdateService"
                  - "ecs:RegisterTaskDefinition"
                  - "ecs:DescribeTaskDefinition"
                  - "ecs:ListTasks"
                  - "ecs:DescribeTasks"
                  - "ecs:CreateService"
                  - "ecs:CreateCluster"
                  - "ecs:CreateTaskSet"
                  - "ecs:UpdateClusterSettings"
                  - "ecs:UpdateServicePrimaryTaskSet"
                  - "ecs:UpdateServiceAutoScaling"
                  - "ecs:PutAccountSetting"
                  - "ecs:PutAccountSettingDefault"
                  - "ecs:PutAttributes"
                  - "ecs:PutClusterCapacityProviders"
                  - "ecs:PutClusterPolicy"
                  - "ecs:PutServiceCapacityProvider"
                  - "ecs:PutServiceConnectConfiguration"
                  - "ecs:CreateTaskSet"
                  - "ecs:UpdateTaskSet"
                  - "ecs:*"
                Effect: Allow
                Resource: "*"
  UnitTestCodeBuild:
    Type: AWS::CodeBuild::Project
    Properties:
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Source:
        Type: CODEPIPELINE
        BuildSpec: devops/pipeline/buildspec_unit_test.yml
      Name: !Sub "${Prefix}-${Environment}-${ResourceType}-unit-tests"
      Description: !Sub 'CodeBuildProjecUnitTest Project for Repo - ${ResourceType}'
  UnitTestCodeBuildLogGroup:
     Type: AWS::Logs::LogGroup
     Properties:
      LogGroupName: !Sub "/aws/codebuild/${UnitTestCodeBuild}"
      RetentionInDays: 30
  BuildPackageCodeBuild: 
    Type: AWS::CodeBuild::Project
    Properties:
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: REGION
            Value: !Ref Region
          - Name: ENVIRONMENT
            Value: !Ref Environment
          - Name: BACKEND_ECR_REPOSITORY_URI
            Value: !GetAtt BackendECRRepository.RepositoryUri
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Source:
        Type: CODEPIPELINE
        BuildSpec: devops/pipeline/buildspec_build_package.yml
      Name: !Sub "${Prefix}-${Environment}-${ResourceType}-BuildAndPackage"
      Description: !Sub 'BuildAndPackage Project for Repo - ${ResourceType}'
  BuildPackageCodeBuildLogGroup:
     Type: AWS::Logs::LogGroup
     Properties:
      LogGroupName: !Sub "/aws/codebuild/${BuildPackageCodeBuild}"
      RetentionInDays: 30
  DeployCodeBuild:
    Type: AWS::CodeBuild::Project
    Properties:
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: PREFIX
            Value: !Ref Prefix
          - Name: ENV_NAME
            Value: !Ref Environment
          - Name: VPC_ID
            Value: !Ref VpcId
          - Name: PUBLIC_SUBNET_1
            Value: !Ref PublicSubnet1
          - Name: PUBLIC_SUBNET_2
            Value: !Ref PublicSubnet2
          - Name: PIPELINE_ARTIFACT_BUCKET_NAME
            Value: !Ref PipelineArtifactBucket
          - Name: REGION
            Value: !Ref Region
          - Name: BACKEND_ECR_REPOSITORY_URI
            Value: !GetAtt BackendECRRepository.RepositoryUri
          - Name: BACKEND_ECR_REPOSITORY_NAME
            Value: !Ref BackendECRRepository
          ## Uncomment the following line if you want to use HTTPS for the ALB
          # - Name: BACKEND_DOMAIN_CERTIFICATE_ARN
          #   Value: !Ref BackendDomainCertificateArn
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Source:
        Type: CODEPIPELINE
        BuildSpec: devops/pipeline/buildspec_deploy.yml
      Name: !Sub "${Prefix}-${Environment}-${ResourceType}-deploy"
      Description: !Sub 'Deploy Project for Repo - ${ResourceType}'
  DeployCodeBuildLogGroup:
     Type: AWS::Logs::LogGroup
     Properties:
      LogGroupName: !Sub "/aws/codebuild/${DeployCodeBuild}"
      RetentionInDays: 30

Outputs:
  CodeBuildServiceRoleArn: 
    Value: !GetAtt CodeBuildServiceRole.Arn
  PipelineExecutionRoleArn: 
    Value: !GetAtt CodePipelineExecutionRole.Arn