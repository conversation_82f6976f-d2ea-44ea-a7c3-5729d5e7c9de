version: 0.2
env:
  git-credential-helper: yes
phases:
  install:
    runtime-versions:
      nodejs: 20
  pre_build:
    commands:
      - pwd
      - ls -al
      - pwd
      - echo docker info 
      - sudo docker info
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $BACKEND_ECR_REPOSITORY_URI
      - docker images -a
  build:
    commands:
      - ls -al
      - echo Build started on `date`
      - echo Building the Docker image...
      - IMAGE_TAG=$(date +%Y%m%d-%H%M%S)
      - docker build -t $BACKEND_ECR_REPOSITORY_URI:$IMAGE_TAG .
      - echo Application Backend Docker Build completed on `date`
      - docker images -a
  post_build:
    commands: 
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $BACKEND_ECR_REPOSITORY_URI:$IMAGE_TAG
artifacts:
  files:
    - '**/**'