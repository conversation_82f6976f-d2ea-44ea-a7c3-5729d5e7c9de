#!/bin/bash

# Exit immediately if any command returns a non-zero exit status
set -e

# Function to handle errors
handle_error() {
  echo "Error: <PERSON><PERSON><PERSON> failed on line $1"
  # Add cleanup actions here if needed
  exit 1
}

# Set the trap to call the error handler function
trap 'handle_error $LINENO' ERR

export Environment=$1

if [ -z "$Environment" ]; then
  echo "ERROR: missing environment name argument."
  echo "USAGE: ./deploy.sh {environment_name}"
  exit 1
fi

echo "Creating/Updating ${Environment} Backend codepipeline"

# Check if the environment file exists
if [ ! -f "../env/${Environment}.sh" ]; then
  echo "ERROR: Environment file not found for ${Environment}"
  exit 1
fi

# Fetch environment specific variables from env file
chmod +x ../env/${Environment}.sh
source ../env/${Environment}.sh

if ! aws s3api head-bucket --bucket $PipelineArtifactBucket 2>/dev/null; then
    echo "Pipeline Artifact Bucket does not exist"
    export ArtifactBucketExists="false"
else
    echo "Bucket already exists."
  export ArtifactBucketExists="true"
fi

# Printing Env variables
echo
echo "Environment variables:"
echo
echo "Environment: $Environment"
echo "Stage: $Stage"
echo "Region: $Region"
echo "Prefix: $Prefix"
echo "Tags: $Tags"
echo "GitBranch: $GitBranch"
echo "Environment: $Environment"
echo "XrayEnabled: $XrayEnabled"
echo "GitHubOwner: $GitHubOwner"
echo "BackendResourceType: $BackendResourceType"
echo "BackendRepositoryName: $BackendRepositoryName"
echo "CodeConnectionsArn: $CodeConnectionsArn"
echo "PipelineArtifactBucket: $PipelineArtifactBucket"
echo "ArtifactBucketExists: $ArtifactBucketExists"
echo "ManualApproval: $ManualApproval"
echo "VpcId: $VpcId"
echo "PublicSubnet1: $PublicSubnet1"
echo "PublicSubnet2: $PublicSubnet2"
echo "PrivateSubnet1: $PrivateSubnet1"
echo "PrivateSubnet2: $PrivateSubnet2"
echo



echo "Deploying ${Environment} Backend codepipeline"

# SAM CLI command to deploy pipeline stack
sam deploy -t codepipeline.yml  \
           --stack-name "${BackendRepositoryName}-${Environment}-pipeline" \
           --region=$Region \
           --no-fail-on-empty-changeset \
           --capabilities=CAPABILITY_NAMED_IAM CAPABILITY_IAM \
           --parameter-overrides Tags=${Tags}\
                                 Stage=${Stage}\
                                 Region=${Region}\
                                 Prefix=${Prefix}\
                                 GitBranch=${GitBranch}\
                                 Environment=${Environment}\
                                 XrayEnabled=${XrayEnabled}\
                                 GitHubOwner=${GitHubOwner}\
                                 ResourceType=${BackendResourceType}\
                                 GitHubRepositoryName=${BackendRepositoryName}\
                                 CodeConnectionsArn=${CodeConnectionsArn}\
                                 PipelineArtifactBucket=${PipelineArtifactBucket}\
                                 ArtifactBucketExists=${ArtifactBucketExists}\
                                 ManualApproval=${ManualApproval}\
                                 VpcId=${VpcId}\
                                 PublicSubnet1=${PublicSubnet1}\
                                 PublicSubnet2=${PublicSubnet2}\
                                 PrivateSubnet1=${PrivateSubnet1}\
                                 PrivateSubnet2=${PrivateSubnet2}\
                                 Tags=${Tags}